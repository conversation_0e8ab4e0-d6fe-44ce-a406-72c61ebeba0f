process.env.BUILD_TARGET = 'server';

require('ts-node').register({ transpileOnly: true, compilerOptions: { module: 'commonjs' } });

const baseConfig = require('./jest.config.base.ts').default || require('./jest.config.base.ts');

module.exports = {
  ...baseConfig,
  testMatch: ['<rootDir>/src/server/**/*.test.js', '<rootDir>/src/server/**/*.test.ts', '<rootDir>/src/server/**/*.test.tsx'],
  transformIgnorePatterns: ['/node_modules/(?!(?:@qantasexperiences/utils|@qantasexperiences/analytics|@qga/roo-ui)/)'],
  testEnvironment: 'node',

  collectCoverageFrom: [
    'src/server/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{js,jsx,ts,tsx}',
    '!src/**/index.{js,jsx,ts,tsx}',
  ],
  coverageDirectory: 'coverage/server',
  coverageReporters: ['text-summary', 'html', 'json-summary'],
  coverageThreshold: {
    global: {
      statements: 92,
      branches: 82,
      functions: 71,
      lines: 93,
    },
  },
};


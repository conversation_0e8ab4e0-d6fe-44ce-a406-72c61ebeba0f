process.env.BUILD_TARGET = 'client';

require('ts-node').register({ transpileOnly: true, compilerOptions: { module: 'commonjs' } });

const baseConfig = require('./jest/jest.config.base.ts').default || require('./jest/jest.config.base.ts');

module.exports = {
  ...baseConfig,
  testMatch: ['<rootDir>/src/**/*.test.js', '<rootDir>/src/**/*.test.ts', '<rootDir>/src/**/*.test.tsx'],
  testPathIgnorePatterns: ['/src/server/'],
  testEnvironment: 'jsdom',
  transformIgnorePatterns: ['/node_modules/(?!connected-next-router|preact).+\\.js$'],

  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/server/**/*',
    '!src/**/*.d.ts',
    '!src/**/*.test.{js,jsx,ts,tsx}',
    '!src/**/index.{js,jsx,ts,tsx}',
  ],
  coverageDirectory: 'coverage/browser',
  coverageReporters: ['text-summary', 'html', 'json-summary'],
  coverageThreshold: {
    global: {
      statements: 93,
      branches: 80,
      functions: 88,
      lines: 93,
    },
  },
};

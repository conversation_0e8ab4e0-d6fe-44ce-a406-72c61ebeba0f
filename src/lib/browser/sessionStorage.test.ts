import * as store from './sessionStorage';
import { captureErrorInSentry } from 'lib/errors';
import { setupMockSessionStorage, setupNoSessionStorage } from 'test-utils';

jest.mock('lib/errors');

const realSessionStorage = window.sessionStorage;

describe('sessionStorage', () => {
  const TEST_KEY_1 = 'TEST_KEY_1';

  beforeEach(() => {
    jest.clearAllMocks();
    realSessionStorage.removeItem(TEST_KEY_1);
  });

  describe('set()', () => {
    describe('when passed an object', () => {
      const input = { message: 'hello' };

      beforeEach(() => {
        store.set(TEST_KEY_1, input);
      });

      it('stores input as JSON string', () => {
        expect(realSessionStorage.getItem(TEST_KEY_1)).toMatchInlineSnapshot(`"{"message":"hello"}"`);
      });
    });

    [123, 'abc', new Date(), true].forEach((input) => {
      describe(`when passed a primitive ${input}`, () => {
        const input = new Date();

        beforeEach(() => {
          store.set(TEST_KEY_1, input);
        });

        it('stores input as JSON string', () => {
          expect(realSessionStorage.getItem(TEST_KEY_1)).toMatch(JSON.stringify(input));
        });
      });
    });

    describe('when session storage is not available', () => {
      const input = { message: 'hello' };

      setupNoSessionStorage();

      it('does not throw', () => {
        expect(() => store.set(TEST_KEY_1, input)).not.toThrow();
      });

      it('does not store anything', () => {
        expect(realSessionStorage.getItem(TEST_KEY_1)).toEqual(null);
      });
    });

    describe('when setItem throws', () => {
      const input = { message: 'hello' };
      const error = new Error('write error');

      const sessionStorageMock = setupMockSessionStorage();

      beforeEach(() => {
        sessionStorageMock.setItem.mockImplementation(() => {
          throw new Error('write error');
        });
      });

      it('does not throw', () => {
        expect(() => store.set(TEST_KEY_1, input)).not.toThrow();
      });

      it('captures error in sentry', () => {
        store.set(TEST_KEY_1, input);
        expect(captureErrorInSentry).toHaveBeenCalledWith(error);
      });
    });
  });

  describe('get()', () => {
    describe('when passed a valid key', () => {
      const input = { message: 'hello' };

      beforeEach(() => {
        store.set(TEST_KEY_1, input);
      });

      it('returns decoded data', () => {
        expect(store.get(TEST_KEY_1)).toEqual(input);
      });
    });

    describe('when passed a key that does not exist', () => {
      it('returns undefined', () => {
        expect(store.get('random_key')).toBeUndefined();
      });
    });

    describe('when session storage is not available', () => {
      setupNoSessionStorage();

      it('does not throw', () => {
        expect(() => store.get(TEST_KEY_1)).not.toThrow();
      });

      it('returns undefined', () => {
        expect(store.get(TEST_KEY_1)).toBeUndefined();
      });
    });

    describe('when getItem throws', () => {
      const error = new Error('write error');

      const sessionStorageMock = setupMockSessionStorage();

      beforeEach(() => {
        sessionStorageMock.getItem.mockImplementation(() => {
          throw error;
        });
      });

      it('does not throw', () => {
        expect(() => store.get(TEST_KEY_1)).not.toThrow();
      });

      it('captures error in sentry', () => {
        store.get(TEST_KEY_1);
        expect(captureErrorInSentry).toHaveBeenCalledWith(error);
      });

      it('returns undefined', () => {
        expect(store.get(TEST_KEY_1)).toBeUndefined();
      });
    });

    describe('when key contains invalid json string', () => {
      beforeEach(() => {
        realSessionStorage.setItem(TEST_KEY_1, '{ message: oops forgot quotes }');
      });

      it('does not throw', () => {
        expect(() => store.get(TEST_KEY_1)).not.toThrow();
      });

      it('returns undefined', () => {
        expect(store.get(TEST_KEY_1)).toBeUndefined();
      });

      it('captures error in sentry', () => {
        store.get(TEST_KEY_1);
        expect(captureErrorInSentry).toHaveBeenCalled();
      });
    });
  });

  describe('merge()', () => {
    describe('when passed an object', () => {
      const input = { message: 'hello' };
      const input2 = { more: 'data' };

      beforeEach(() => {
        store.set(TEST_KEY_1, input);
        store.merge(TEST_KEY_1, input2);
      });

      it('stores data as merged json string', () => {
        expect(realSessionStorage.getItem(TEST_KEY_1)).toEqual(`{"message":"hello","more":"data"}`);
      });

      it('data can be decoded', () => {
        expect(store.get(TEST_KEY_1)).toEqual({ message: 'hello', more: 'data' });
      });
    });

    describe('when the key is empty', () => {
      const input2 = { more: 'data' };

      beforeEach(() => {
        realSessionStorage.removeItem(TEST_KEY_1);
        store.merge(TEST_KEY_1, input2);
      });

      it('stores data as json string', () => {
        expect(realSessionStorage.getItem(TEST_KEY_1)).toEqual(`{"more":"data"}`);
      });

      it('data can be decoded', () => {
        expect(store.get(TEST_KEY_1)).toEqual({ more: 'data' });
      });
    });

    describe('when session storage in not available', () => {
      const input = { message: 'hello' };

      setupNoSessionStorage();

      it('does not throw', () => {
        expect(() => store.merge(TEST_KEY_1, input)).not.toThrow();
      });

      it('does not store anything', () => {
        expect(realSessionStorage.getItem(TEST_KEY_1)).toEqual(null);
      });
    });

    describe('when setItem throws', () => {
      const input = { message: 'hello' };
      const error = new Error('write error');

      const sessionStorageMock = setupMockSessionStorage();

      beforeEach(() => {
        sessionStorageMock.setItem.mockImplementation(() => {
          throw new Error('write error');
        });
      });

      it('does not throw', () => {
        expect(() => store.merge(TEST_KEY_1, input)).not.toThrow();
      });

      it('captures error in sentry', () => {
        store.merge(TEST_KEY_1, input);
        expect(captureErrorInSentry).toHaveBeenCalledWith(error);
      });
    });
  });

  describe('remove()', () => {
    const TEST_KEY_1 = 'TEST_KEY_1';
    const TEST_KEY_2 = 'TEST_KEY_2';
    const value1 = { data: 'some data' };
    const value2 = { data: 'other data' };

    beforeEach(() => {
      store.set(TEST_KEY_1, value1);
      store.set(TEST_KEY_2, value2);
    });

    describe('when called with a valid key', () => {
      it('removes the specified item from session storage', () => {
        store.remove(TEST_KEY_1);
        expect(realSessionStorage.getItem(TEST_KEY_1)).toBeNull();
      });

      it('does not affect other items in session storage', () => {
        store.remove(TEST_KEY_1);
        expect(store.get(TEST_KEY_2)).toEqual(value2);
      });
    });

    describe('when session storage is not available', () => {
      setupNoSessionStorage();

      it('does not throw an error', () => {
        expect(() => store.remove(TEST_KEY_1)).not.toThrow();
      });
    });

    describe('when removeItem throws an error', () => {
      const error = new Error('remove error');
      setupMockSessionStorage();

      beforeEach(() => {
        (window.sessionStorage.removeItem as jest.Mock).mockImplementation(() => {
          throw error;
        });
      });

      it('does not throw an error', () => {
        expect(() => store.remove(TEST_KEY_1)).not.toThrow();
      });

      it('captures the error in Sentry', () => {
        store.remove(TEST_KEY_1);
        expect(captureErrorInSentry).toHaveBeenCalledWith(error);
      });
    });
  });

  describe('clear()', () => {
    const TEST_KEY_1 = 'TEST_KEY_1';
    const TEST_KEY_2 = 'TEST_KEY_2';

    beforeEach(() => {
      store.set(TEST_KEY_1, { data: 'some data' });
      store.set(TEST_KEY_2, { data: 'other data' });
    });

    describe('when session storage has items', () => {
      it('removes all items from session storage', () => {
        expect(realSessionStorage.getItem(TEST_KEY_1)).not.toBeNull();
        expect(realSessionStorage.getItem(TEST_KEY_2)).not.toBeNull();

        store.clear();

        expect(realSessionStorage.getItem(TEST_KEY_1)).toBeNull();
        expect(realSessionStorage.getItem(TEST_KEY_2)).toBeNull();
        expect(realSessionStorage).toHaveLength(0);
      });
    });

    describe('when session storage is not available', () => {
      setupNoSessionStorage();

      it('does not throw an error', () => {
        expect(() => store.clear()).not.toThrow();
      });
    });

    describe('when clear throws an error', () => {
      const error = new Error('clear error');
      setupMockSessionStorage();

      beforeEach(() => {
        (window.sessionStorage.clear as jest.Mock).mockImplementation(() => {
          throw error;
        });
      });

      it('does not throw an error', () => {
        expect(() => store.clear()).not.toThrow();
      });

      it('captures the error in Sentry', () => {
        store.clear();
        expect(captureErrorInSentry).toHaveBeenCalledWith(error);
      });
    });
  });

  describe('sessionStorage edge cases', () => {
    const TEST_KEY = 'EDGE_CASE_KEY';

    beforeEach(() => {
      jest.clearAllMocks();
      realSessionStorage.removeItem(TEST_KEY);
    });

    describe('__proto__ pollution', () => {
      it('get() should not allow prototype pollution', () => {
        const maliciousPayload = '{"__proto__":{"polluted":"true"}}';
        realSessionStorage.setItem(TEST_KEY, maliciousPayload);

        const result = store.get(TEST_KEY);

        // Ensure the object prototype is not polluted
        expect({}['polluted']).toBeUndefined();
        // The getter should return an empty object as __proto__ is stripped
        expect(result).toEqual({});
      });

      it('merge() should not allow prototype pollution', () => {
        const initialData = { message: 'hello' };
        const maliciousUpdate = JSON.parse('{"__proto__":{"polluted":"true"}}');

        store.set(TEST_KEY, initialData);
        store.merge(TEST_KEY, maliciousUpdate);

        // Ensure the object prototype is not polluted
        expect({}['polluted']).toBeUndefined();

        // The merged object in storage should contain both original and new data
        const result = store.get(TEST_KEY);
        expect(result).toEqual({ message: 'hello' });
      });
    });

    describe('unsupported data types', () => {
      it('should handle objects with undefined, Symbol, NaN, and Infinity', () => {
        const complexObject = {
          a: undefined,
          b: Symbol('test'),
          c: NaN,
          d: Infinity,
          e: 'hello',
          f: [-Infinity, undefined],
        };

        store.set(TEST_KEY, complexObject);
        const result = store.get(TEST_KEY);

        // JSON.stringify omits properties with undefined/Symbol values
        // and converts NaN/Infinity to null.
        expect(result).toEqual({
          c: null,
          d: null,
          e: 'hello',
          f: [null, null],
        });
      });
    });

    describe('circular references', () => {
      it('set() should not throw on circular references and should capture error', () => {
        type CircularObject = {
          a?: { b: CircularObject };
        };

        const obj: CircularObject = {};
        obj.a = { b: obj }; // Circular reference

        expect(() => store.set(TEST_KEY, obj)).not.toThrow();
        expect(captureErrorInSentry).toHaveBeenCalledWith(expect.any(TypeError));
        expect((captureErrorInSentry as jest.Mock).mock.calls[0][0].message).toContain('circular structure');
      });
    });

    describe('storage quota exceeded', () => {
      const sessionStorageMock = setupMockSessionStorage();
      const quotaError = new DOMException('Quota exceeded', 'QuotaExceededError');

      beforeEach(() => {
        sessionStorageMock.setItem.mockImplementation(() => {
          throw quotaError;
        });
      });

      it('set() should capture QuotaExceededError', () => {
        expect(() => store.set(TEST_KEY, { data: 'large data' })).not.toThrow();
        expect(captureErrorInSentry).toHaveBeenCalledWith(quotaError);
      });

      it('merge() should capture QuotaExceededError', () => {
        expect(() => store.merge(TEST_KEY, { data: 'large data' })).not.toThrow();
        expect(captureErrorInSentry).toHaveBeenCalledWith(quotaError);
      });
    });

    describe('merging into non-objects', () => {
      it('should handle merging an object into a stored string', () => {
        store.set(TEST_KEY, 'hello');
        store.merge(TEST_KEY, { world: true });

        const result = store.get(TEST_KEY);
        expect(result).toEqual({
          '0': 'h',
          '1': 'e',
          '2': 'l',
          '3': 'l',
          '4': 'o',
          world: true,
        });
      });

      it('should handle merging an object into a stored null value', () => {
        store.set(TEST_KEY, null);
        store.merge(TEST_KEY, { world: true });

        const result = store.get(TEST_KEY);
        expect(result).toEqual({ world: true });
      });
    });
  });
});

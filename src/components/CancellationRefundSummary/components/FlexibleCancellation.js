import { isBefore } from 'date-fns';
import React from 'react';
import { Flex, Icon, Text } from '@qga/roo-ui/components';
import TextButton from 'components/TextButton';
import PropTypes from 'prop-types';
import { resolveFirstCancellationWindowWithPenalty } from 'components/CancellationRefundSummary/utils';

const FlexibleCancellation = ({
  cancellationWindows,
  handleOnClick,
  fontSize,
  hideBeforeDate,
  hideWhenNonRefundable,
  isSpaceBetween = false,
}) => {
  const firstCancellationWindowWithPenalty = resolveFirstCancellationWindowWithPenalty(cancellationWindows);

  if (!firstCancellationWindowWithPenalty || isBefore(new Date(), new Date(firstCancellationWindowWithPenalty.startTime))) {
    return (
      <Flex flexWrap="wrap" alignItems="center" justifyContent={isSpaceBetween ? 'space-between' : 'flex-start'}>
        <Flex flexDirection="row" alignItems="center">
          <Icon name="freeCancellation" color="darkGreen" size={16} mt="3px" mr={1} />
          {handleOnClick ? (
            <TextButton
              onClick={handleOnClick}
              fontSize={fontSize}
              fontWeight="bold"
              color="darkGreen"
              hoverColor="darkGreen"
              data-testid="cancellation-policy-message"
            >
              Free cancellation
            </TextButton>
          ) : (
            <Text fontSize={fontSize} fontWeight="bold" color="darkGreen" data-testid="cancellation-policy-message">
              Free cancellation
            </Text>
          )}
        </Flex>
        {firstCancellationWindowWithPenalty && !hideBeforeDate && (
          <Text color="greys.steel" display="block" fontSize={fontSize} data-testid="free-cancellation-before-date" pl={1}>
            before {firstCancellationWindowWithPenalty.formattedBeforeDate}
          </Text>
        )}
      </Flex>
    );
  } else if (hideWhenNonRefundable) {
    return null;
  } else {
    return handleOnClick ? (
      <Flex alignItems={'center'} alignContent={'center'} mb={5}>
        <Icon name="infoOutline" size={16} mr={2} />
        <TextButton
          onClick={handleOnClick}
          fontSize={fontSize}
          fontWeight="bold"
          color="greys.steel"
          hoverColor="greys.steel"
          data-testid="cancellation-policy-message"
        >
          Cancellation Policy
        </TextButton>
      </Flex>
    ) : (
      <Flex alignItems={'center'} alignContent={'center'} mb={5}>
        <Icon name="infoOutline" size={16} mr={2} />
        <Text fontSize={fontSize} fontWeight="bold" color="greys.steel" data-testid="cancellation-policy-message">
          Cancellation Policy
        </Text>
      </Flex>
    );
  }
};

FlexibleCancellation.propTypes = {
  cancellationWindows: PropTypes.array.isRequired,
  handleOnClick: PropTypes.func,
  fontSize: PropTypes.oneOfType([PropTypes.string, PropTypes.array]),
  hideBeforeDate: PropTypes.bool.isRequired,
  hideWhenNonRefundable: PropTypes.bool.isRequired,
  isSpaceBetween: PropTypes.bool,
};

FlexibleCancellation.defaultProps = {
  handleOnClick: null,
};

export default FlexibleCancellation;

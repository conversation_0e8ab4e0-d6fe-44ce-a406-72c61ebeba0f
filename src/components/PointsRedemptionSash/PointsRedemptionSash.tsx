import React from 'react';
import { Badge as RooBadge, BadgeTypeProp } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';

const Badge = styled(RooBadge)`
  ${themeGet('badge')}
`;

interface PointsRedemptionSashProps {
  campaignMessage: string;
  type?: BadgeTypeProp;
  backgroundColor?: string;
  color?: string;
}

const PointsRedemptionSash = ({ campaignMessage, type = 'default', backgroundColor, color }: PointsRedemptionSashProps) => {
  return (
    <Badge
      text={campaignMessage}
      size="rg"
      color={color}
      backgroundColor={backgroundColor}
      fontWeight="bold"
      type={type}
      textTransform="capitalize"
    />
  );
};

export default PointsRedemptionSash;

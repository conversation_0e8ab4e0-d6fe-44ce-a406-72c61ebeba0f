// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<PointRedemptionSash /> when campaignMessage prop is received renders the points redemption sash 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="erl8y0h0 css-1g4bo05-Box-Flex-BadgeFrame-Badge e1l8te9n0"
      >
        <span
          class="css-z0e5wf-Text-BadgeText e34cw120"
        >
          Save 10,000 PTS
        </span>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="erl8y0h0 css-1g4bo05-Box-Flex-BadgeFrame-Badge e1l8te9n0"
    >
      <span
        class="css-z0e5wf-Text-BadgeText e34cw120"
      >
        Save 10,000 PTS
      </span>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

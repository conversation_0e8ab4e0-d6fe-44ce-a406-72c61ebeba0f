import React from 'react';
import { Box, Flex, Icon, Text } from '@qga/roo-ui/components';
import get from 'lodash/get';
import Currency from 'components/Currency';
import CampaignPriceMessage from 'components/CampaignPriceMessage';
import CancellationTooltip from 'components/CancellationTooltip/CancellationTooltip';
import CancellationRefundModal from 'components/CancellationRefundModal';
import NightsAndGuests from 'components/NightsAndGuests';
import OfferDepositPayMessage from '../../../OfferDepositPayMessage';
import RoomsAvailabilityMessage from 'components/RoomsAvailabilityMessage';
import PriceStrikethrough from 'components/PriceStrikethrough';
import usePriceStrikethrough from 'hooks/optimizely/usePriceStrikethrough/usePriceStrikethrough';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import { useBreakpoints } from 'hooks/useBreakpoints';
import type { Offer } from 'types/property';
import PriceBeforeDiscount from 'components/PriceBeforeDiscount';
import usePointsRedemptionStrikethrough from 'hooks/optimizely/usePointsRedemptionStrikethrough';
import PointsRedemptionSash from 'components/PointsRedemptionSash';
import { FormatNumber } from 'components/formatters';

interface OfferPricingProps {
  charges: Offer['charges'];
  allocationsAvailable: Offer['allocationsAvailable'];
  cancellationPolicy: Offer['cancellationPolicy'];
  depositPay: Offer['depositPay'];
  isClassic: boolean;
  offerType: Offer['type'];
  isLuxuryOffer?: boolean;
}

export const OfferPricing = ({
  isClassic,
  charges,
  allocationsAvailable,
  cancellationPolicy,
  depositPay,
  offerType,
  isLuxuryOffer,
}: OfferPricingProps) => {
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(0);
  const { total, totalBeforeDiscount, totalDiscount } = charges;
  const { showFullTraffic, showVariationB } = usePriceStrikethrough();
  const isPriceStrikethrough = showFullTraffic || showVariationB;
  const strikethroughPrice = charges?.strikethrough?.price;
  const isPriceStrikeAvailable = !!strikethroughPrice?.amount;
  const showPriceStrikethrough = isPriceStrikeAvailable && isPriceStrikethrough && !isClassic;

  const currency = get(charges, 'total.currency');
  const isCurrencyPoints = currency === 'PTS';
  const isCurrencyCash = currency !== 'PTS';
  const hasDiscount = totalDiscount?.amount > 0;
  const { isPointsRedemptionStrikethrough } = usePointsRedemptionStrikethrough();
  const showPointsRedemptionStrikethrough =
    !isCurrencyCash && !isLuxuryOffer && hasDiscount && !isClassic && isPointsRedemptionStrikethrough;
  const pointsValue = FormatNumber({ number: charges.totalDiscount?.amount ?? 0, decimalPlaces: 0 });

  const { showMessage, max_rooms_cutoff } = useAvailableRoomsMessage();

  const availableRoomsMaxCutoff = max_rooms_cutoff ?? 5;
  const showAvailableRooms =
    showMessage && allocationsAvailable && allocationsAvailable > 0 && allocationsAvailable <= availableRoomsMaxCutoff;

  const isDepositPay = depositPay?.depositPayable;

  return (
    <Flex
      data-testid="right-column-offer-card"
      flexDirection={['column']}
      alignItems={['flex-start', 'flex-end', 'flex-end']}
      width={['100%', '50%', '50%']}
      pl={[0, 6, 0]}
    >
      <Flex flexDirection="column" data-testid="collapsed-offers" alignSelf={'flex-start'}>
        <Flex flexDirection="column">
          <Flex flexDirection="column" justifyContent="center" alignItems="space-between" borderLeft={3} borderColor={'red'} pl={2} mb={3}>
            <NightsAndGuests />
            <Flex flexDirection="row" alignItems="baseline" flexWrap="wrap">
              <Flex alignItems="center">
                {isClassic && <Icon name="ribbon" mr="2" color="greys.charcoal" />}
                <Currency
                  amount={total.amount}
                  currency={total.currency}
                  roundToCeiling
                  fontSize={'32px'}
                  fontWeight={600}
                  hideCurrency={true}
                  color="greys.charcoal"
                  data-testid="total-to-pay-new"
                  alignCurrency="superscript"
                />
                {!showPriceStrikethrough && (
                  <Text pl={1} pr={2} pb="10px" fontWeight={'bold'}>
                    {currency}
                    {isCurrencyPoints && <sup data-testid="asterisk">*</sup>}
                  </Text>
                )}
                {showPriceStrikethrough && (
                  <Box pb={2}>
                    <PriceStrikethrough price={strikethroughPrice} ml={2} mt={isCurrencyPoints ? 2 : 3} data-testid="price-strikethrough" />
                  </Box>
                )}
                {showPointsRedemptionStrikethrough && (
                  <PriceBeforeDiscount
                    total={totalBeforeDiscount}
                    discount={totalDiscount}
                    fontSize={['xs', 'sm']}
                    lineHeight="0.6"
                    roundToCeiling
                    offerType={offerType}
                    showMessage={false}
                    pb="10px"
                  />
                )}
              </Flex>
            </Flex>
            {showPointsRedemptionStrikethrough ? (
              <Box mt={1}>
                <PointsRedemptionSash campaignMessage={`Save ${pointsValue} PTS`} backgroundColor="#0F7401" color="white" />
              </Box>
            ) : (
              <CampaignPriceMessage
                currency={total.currency}
                offerType={offerType}
                fallback
                color={showAvailableRooms ? 'greys.steel' : undefined}
              />
            )}
          </Flex>
          {showAvailableRooms && <RoomsAvailabilityMessage allocationsAvailable={allocationsAvailable} isOffer={true} />}
          {!isMobile && (
            <Box>
              <CancellationTooltip cancellationPolicy={cancellationPolicy} data-testid="cancellation-tooltip" />
              {isDepositPay && <OfferDepositPayMessage depositPay={depositPay} />}
            </Box>
          )}
        </Flex>
      </Flex>
      {isMobile && (
        <>
          <Box mt={2}>
            <CancellationRefundModal cancellationPolicy={cancellationPolicy} fontSize={['xs', 'sm']} />
          </Box>
          {isDepositPay && (
            <Box mb={[2, 10, 10]}>
              <OfferDepositPayMessage depositPay={depositPay} />
            </Box>
          )}
        </>
      )}
    </Flex>
  );
};

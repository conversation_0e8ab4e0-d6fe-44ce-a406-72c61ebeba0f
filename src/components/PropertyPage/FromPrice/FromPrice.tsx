import React, { memo, useCallback } from 'react';
import isEmpty from 'lodash/isEmpty';
import { Box, Flex, Text, Link } from '@qga/roo-ui/components';
import Currency from 'components/Currency';
import CampaignPriceMessage from 'components/CampaignPriceMessage';
import { getAvailableRoomTypes, getHasValidQuery, getIsLoading } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { getQueryCheckIn, getQueryCheckOut, getQueryAdults, getQueryChildren, getQueryInfants } from 'store/router/routerSelectors';
import NumberOfNights from 'components/NumberOfNights';
import { useSelector } from 'react-redux';
import { useDataLayer } from 'hooks/useDataLayer';
import ResultLoader from 'components/Loader/ResultLoader';
import LoaderSkeletonCard from './LoaderSkeletonCard';
import { useBreakpoints } from 'hooks/useBreakpoints';
import CheckAvailabilityButton from '../CheckAvailabilityButton';
import { ButtonLink, DataWrapper, NightsAndGuestsWrapper, Wrapper } from './FromPrice.style';
import useCampaignPriceMessage from 'components/CampaignPriceMessage/useCampaignPriceMessage';
import useCtaClickEvent from 'hooks/useCtaClickEvent';
import PriceBeforeDiscount from 'components/PriceBeforeDiscount';
import usePointsRedemptionStrikethrough from 'hooks/optimizely/usePointsRedemptionStrikethrough';
import PointsRedemptionSash from 'components/PointsRedemptionSash';
import { FormatNumber } from 'components/formatters';

const NightsAndGuests = () => {
  const checkIn = useSelector(getQueryCheckIn);
  const checkOut = useSelector(getQueryCheckOut);
  const adults = useSelector(getQueryAdults);
  const children = useSelector(getQueryChildren);
  const infants = useSelector(getQueryInfants);
  const totalGuests = adults + children + infants;

  return (
    <>
      <NightsAndGuestsWrapper data-testid="nights-guests-new">
        <Text fontSize={'16px'} color={'greys.steel'} fontWeight={400}>
          {' '}
          {totalGuests} guests &nbsp;•{' '}
        </Text>
        <NumberOfNights checkIn={checkIn} checkOut={checkOut} color={'greys.steel'} font={'16px'} fontWeight={400} />
        <Text fontSize={'16px'} color={'greys.steel'} fontWeight={400}>
          {' '}
          from
        </Text>
      </NightsAndGuestsWrapper>
    </>
  );
};

const OfferPrice = () => {
  const { emitInteractionEvent } = useDataLayer();
  const availableRoomTypes = useSelector(getAvailableRoomTypes);
  const leadingOffer = availableRoomTypes[0]?.offers[0] ?? {};
  const charges = leadingOffer?.charges ?? {};
  const amount = charges.total?.amount ?? 0;
  const currency = charges.total?.currency ?? '';
  const hasAvailability = amount !== 0;
  const isCurrencyPoints = currency === 'PTS';
  const offerType = leadingOffer.type;
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(0);
  const isClassic = leadingOffer?.type === 'classic';
  const isLuxuryOffer = leadingOffer?.luxOffer;
  const hasDiscount = charges?.totalDiscount?.amount > 0;
  const isCurrencyCash = currency === 'AUD' || currency === 'USD' || currency === 'NZD';
  const { isPointsRedemptionStrikethrough } = usePointsRedemptionStrikethrough();
  const showPointsRedemptionStrikethrough =
    isCurrencyPoints && !isLuxuryOffer && hasDiscount && !isClassic && isPointsRedemptionStrikethrough;

  const { showCampaignPriceMessage, campaignPriceMessage } = useCampaignPriceMessage({
    currency: currency,
    fallback: true,
  });
  const pointsValue = FormatNumber({ number: charges.totalDiscount?.amount ?? 0, decimalPlaces: 0 });

  const { ctaClickEvent } = useCtaClickEvent();

  const handleOnClick = useCallback(() => {
    ctaClickEvent({ itemText: 'View Rooms', itemType: 'button', url: '#view-rooms' });
    emitInteractionEvent({ type: 'From Price', value: `View Room Selected` });
  }, [ctaClickEvent, emitInteractionEvent]);

  if (isEmpty(availableRoomTypes)) return null;

  return (
    <Box
      width={showCampaignPriceMessage && campaignPriceMessage ? ['100%', 430, 390, 460] : ['100%', 450, 440]}
      height={['100%', 79, 79]}
      pr={[0, 6, 0]}
      pl={[0, 2, 0]}
    >
      {hasAvailability && (
        <Wrapper data-testid="from-price-box" pr={1} pl={[0, 0, 0, 10]} pt={[3, 0, 0]} pb={[6, 0, 0]}>
          <DataWrapper>
            <Flex
              flexDirection="column"
              justifyContent="center"
              alignItems="space-between"
              borderLeft={3}
              borderColor="red"
              pl={3}
              pt={[0, 1, 1]}
              height={showPointsRedemptionStrikethrough ? '90px' : '85px'}
              width={showCampaignPriceMessage && campaignPriceMessage ? '100%' : 'auto'}
            >
              <NightsAndGuests />
              <Flex flexDirection="row" alignItems="flex-start" flexWrap="wrap" data-testid="currency-box">
                <Currency
                  amount={amount}
                  currency={currency}
                  roundToCeiling
                  fontSize={'32px'}
                  fontWeight={'bold'}
                  hideCurrency={true}
                  color="greys.charcoal"
                  data-testid="total-to-pay"
                  alignCurrency="superscript"
                  fromPrice
                />
                <Text pl={1} mr={3} fontWeight={'bold'}>
                  {currency}
                  {isCurrencyPoints && <sup>*</sup>}
                </Text>
                {showPointsRedemptionStrikethrough && (
                  <Box mt={1}>
                    <PriceBeforeDiscount
                      total={charges.totalBeforeDiscount}
                      discount={charges.totalDiscount}
                      hideCurrency={isCurrencyCash}
                      fontSize={['xs', 'sm']}
                      lineHeight="0.6"
                      roundToCeiling
                      offerType={leadingOffer.type}
                      showMessage={false}
                    />
                  </Box>
                )}
                {!isMobile && showPointsRedemptionStrikethrough && (
                  <Box mt={1}>
                    <PointsRedemptionSash campaignMessage={`Save ${pointsValue} PTS`} backgroundColor="#0F7401" color="white" />
                  </Box>
                )}
                {!isMobile && !showPointsRedemptionStrikethrough && (
                  <CampaignPriceMessage currency={currency} offerType={offerType} fallback />
                )}
              </Flex>
              {isMobile && showPointsRedemptionStrikethrough && (
                <Box mt={1}>
                  <PointsRedemptionSash campaignMessage={`Save ${pointsValue} PTS`} backgroundColor="#0F7401" color="white" />
                </Box>
              )}
              {isMobile && !showPointsRedemptionStrikethrough && (
                <CampaignPriceMessage currency={currency} offerType={offerType} fallback />
              )}
            </Flex>
          </DataWrapper>
          <Flex flexDirection="column" justifyContent="flex-start">
            {!isMobile && (
              <ButtonLink
                as={Link}
                variant="primary"
                data-testid="view-rooms"
                href="#view-rooms"
                aria-label={`View rooms`}
                onClick={handleOnClick}
                paddingBottom={4}
                marginBottom={showPointsRedemptionStrikethrough ? 1 : 6}
              >
                View Rooms
              </ButtonLink>
            )}
          </Flex>
        </Wrapper>
      )}
    </Box>
  );
};

const FromPrice = memo(() => {
  const hasValidQuery = useSelector(getHasValidQuery);
  const isLoading = useSelector(getIsLoading);
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(0);

  if (!hasValidQuery && !isMobile) return <CheckAvailabilityButton />;

  if (!hasValidQuery && isMobile) return null;

  return (
    <Box mx={[3, 0, 0]}>
      <ResultLoader isLoading={isLoading} skeletonResultCount={1} skeletonCardComponent={LoaderSkeletonCard}>
        <OfferPrice />
      </ResultLoader>
    </Box>
  );
});

FromPrice.displayName = 'FromPrice';

export default FromPrice;
